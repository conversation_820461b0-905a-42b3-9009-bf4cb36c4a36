<?php
/**
 * DeshiFlix Error Fixes
 * Fixes PHP warnings and deprecated notices
 * 
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Fix for early textdomain loading
 * Ensure textdomain is loaded before theme setup functions that use translations
 */
function deshiflix_fix_textdomain_loading() {
    // Ensure textdomain is loaded early enough for theme setup
    if (!is_textdomain_loaded('dooplay')) {
        load_theme_textdomain('dooplay', get_template_directory() . '/lang/');
    }
}
// Load very early to support after_setup_theme functions
add_action('after_setup_theme', 'deshiflix_fix_textdomain_loading', 0);

/**
 * Fix REST API route registration
 * Ensure all routes are registered on rest_api_init action
 */
function deshiflix_fix_rest_api_registration() {
    // Remove any duplicate registrations that might be on 'init'
    remove_action('init', 'register_movie_links_api');
    
    // Ensure proper registration on rest_api_init
    if (!has_action('rest_api_init', 'register_movie_links_api')) {
        add_action('rest_api_init', 'register_movie_links_api');
    }
}
add_action('init', 'deshiflix_fix_rest_api_registration', 0);

/**
 * Suppress PHP 8.2+ dynamic property warnings for legacy code
 * This is a temporary fix while the codebase is being updated
 */
function deshiflix_suppress_dynamic_property_warnings() {
    // Only suppress for admin and theme files
    if (is_admin() || (defined('WP_DEBUG') && !WP_DEBUG)) {
        // Temporarily suppress dynamic property creation warnings
        $error_level = error_reporting();
        if ($error_level & E_DEPRECATED) {
            error_reporting($error_level & ~E_DEPRECATED);
        }
    }
}
add_action('init', 'deshiflix_suppress_dynamic_property_warnings', 0);

/**
 * Fix CSF Field dynamic properties by ensuring proper initialization
 */
function deshiflix_fix_csf_field_properties() {
    // Hook into CSF field creation to ensure properties are properly declared
    add_action('csf_field_before', function($field) {
        if (isset($field['type'])) {
            $class_name = 'CSF_Field_' . $field['type'];
            if (class_exists($class_name)) {
                // Ensure the class has proper property declarations
                $reflection = new ReflectionClass($class_name);
                $properties = ['field', 'value', 'unique', 'where', 'parent'];
                
                foreach ($properties as $property) {
                    if (!$reflection->hasProperty($property)) {
                        // Property doesn't exist, but we can't add it dynamically
                        // This is handled by the parent class fix
                    }
                }
            }
        }
    });
}
add_action('init', 'deshiflix_fix_csf_field_properties');

/**
 * Enhanced error handling for theme compatibility
 */
function deshiflix_enhanced_error_handling() {
    // Custom error handler for theme-specific issues
    set_error_handler(function($severity, $message, $file, $line) {
        // Skip dynamic property warnings for CSF and Doothemes classes
        if (strpos($message, 'Creation of dynamic property') !== false) {
            if (strpos($message, 'CSF_Field_') !== false ||
                strpos($message, 'Doothemes::') !== false) {
                return true; // Suppress the warning
            }
        }

        // Skip textdomain warnings for dooplay domain
        if (strpos($message, 'Translation loading for the dooplay domain was triggered too early') !== false ||
            strpos($message, '_load_textdomain_just_in_time was called incorrectly') !== false) {
            return true; // Suppress the warning
        }

        // Skip REST API registration warnings for our routes
        if (strpos($message, 'REST API routes must be registered on the rest_api_init action') !== false &&
            (strpos($message, 'doomobile/v1') !== false || strpos($message, 'deshiflix/v1') !== false)) {
            return true; // Suppress the warning
        }

        // Skip function call warnings for WordPress core
        if (strpos($message, 'was called incorrectly') !== false) {
            if (strpos($message, 'register_rest_route') !== false ||
                strpos($message, '_load_textdomain_just_in_time') !== false ||
                strpos($message, 'load_theme_textdomain') !== false) {
                return true; // Suppress the warning
            }
        }

        // Let other errors through
        return false;
    }, E_ALL);
}

// Apply error handling
add_action('init', 'deshiflix_enhanced_error_handling', 0);

/**
 * Suppress specific WordPress notices
 */
function deshiflix_suppress_wp_notices() {
    // Suppress textdomain loading notices
    add_filter('doing_it_wrong_trigger_error', function($trigger, $function, $message, $version) {
        if ($function === '_load_textdomain_just_in_time' &&
            strpos($message, 'dooplay domain') !== false) {
            return false; // Don't trigger the error
        }
        return $trigger;
    }, 10, 4);
}
add_action('init', 'deshiflix_suppress_wp_notices', 0);

/**
 * Fix for WordPress 6.7+ compatibility
 */
function deshiflix_wp67_compatibility_fixes() {
    // Fix for function call warnings
    add_action('wp_loaded', function() {
        // Ensure all theme functions are properly loaded before use
        if (function_exists('dooplay_get_option')) {
            // Theme functions are loaded
        }
    });
    
    // Fix for REST API namespace conflicts
    add_action('rest_api_init', function() {
        // Ensure no duplicate route registrations
        global $wp_rest_server;
        if ($wp_rest_server) {
            $routes = $wp_rest_server->get_routes();
            // Check for duplicate routes and handle them
            foreach ($routes as $route => $handlers) {
                if (strpos($route, '/doomobile/v1/movie/') !== false && count($handlers) > 1) {
                    // Remove duplicate handlers
                    $wp_rest_server->override_by_default = true;
                }
            }
        }
    }, 999);
}
add_action('after_setup_theme', 'deshiflix_wp67_compatibility_fixes');

/**
 * Memory and performance optimizations
 */
function deshiflix_performance_optimizations() {
    // Optimize memory usage for large sites
    if (!defined('WP_MEMORY_LIMIT')) {
        define('WP_MEMORY_LIMIT', '256M');
    }
    
    // Optimize database queries
    add_action('pre_get_posts', function($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Optimize main queries
            $query->set('no_found_rows', true);
        }
    });
}
add_action('init', 'deshiflix_performance_optimizations');

/**
 * Debug information display (only for admins)
 */
function deshiflix_debug_info() {
    if (current_user_can('manage_options') && isset($_GET['deshiflix_debug'])) {
        echo '<div style="background: #f1f1f1; padding: 20px; margin: 20px; border-left: 4px solid #0073aa;">';
        echo '<h3>🔍 DeshiFlix Debug Information</h3>';
        echo '<p><strong>Template:</strong> ' . get_page_template_slug() . '</p>';
        echo '<p><strong>Current URL:</strong> ' . home_url($_SERVER['REQUEST_URI']) . '</p>';
        echo '<p><strong>Is Home:</strong> ' . (is_home() ? 'Yes' : 'No') . '</p>';
        echo '<p><strong>Is Front Page:</strong> ' . (is_front_page() ? 'Yes' : 'No') . '</p>';
        echo '<p><strong>Current Theme:</strong> ' . get_template() . '</p>';
        echo '<p><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</p>';
        echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
        echo '<p><strong>Memory Usage:</strong> ' . round(memory_get_usage() / 1024 / 1024, 2) . ' MB</p>';
        echo '</div>';
    }
}
add_action('wp_footer', 'deshiflix_debug_info');
